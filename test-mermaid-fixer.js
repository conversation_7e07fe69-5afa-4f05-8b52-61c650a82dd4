// 测试 Mermaid 修复功能的脚本
import { autoFixMermaidCode, toggleMermaidDirection } from './lib/mermaid-fixer.js';

// 测试用例
const testCases = [
  {
    name: "特殊字符修复测试",
    input: `flowchart TD
    A[开始] --> B{是否包含特殊字符?}
    B --> C[处理(特殊)字符]
    C --> D[结束]`,
    expected: "应该给包含特殊字符的节点添加引号"
  },
  {
    name: "方向切换测试 - TD to LR",
    input: `flowchart TD
    A --> B
    B --> C`,
    expected: "应该将 TD 改为 LR"
  },
  {
    name: "方向切换测试 - LR to TD",
    input: `flowchart LR
    A --> B
    B --> C`,
    expected: "应该将 LR 改为 TD"
  },
  {
    name: "复杂特殊字符测试",
    input: `flowchart TD
    A[用户登录] --> B{验证成功?}
    B -->|是| C[跳转到主页面]
    B -->|否| D[显示错误信息]
    C --> E[加载用户数据(包含特殊字符@#$)]`,
    expected: "应该修复所有特殊字符问题"
  }
];

console.log("🧪 开始测试 Mermaid 修复功能...\n");

// 测试自动修复功能
console.log("📝 测试自动修复功能:");
testCases.slice(0, 2).forEach((testCase, index) => {
  console.log(`\n${index + 1}. ${testCase.name}`);
  console.log("输入:");
  console.log(testCase.input);
  
  const result = autoFixMermaidCode(testCase.input);
  console.log("\n修复后:");
  console.log(result);
  console.log(`\n期望: ${testCase.expected}`);
  console.log("---");
});

// 测试方向切换功能
console.log("\n🔄 测试方向切换功能:");
testCases.slice(1, 3).forEach((testCase, index) => {
  console.log(`\n${index + 1}. ${testCase.name}`);
  console.log("输入:");
  console.log(testCase.input);
  
  const result = toggleMermaidDirection(testCase.input);
  console.log("\n切换后:");
  console.log(result);
  console.log(`\n期望: ${testCase.expected}`);
  console.log("---");
});

console.log("\n✅ 测试完成！");
console.log("\n💡 使用说明:");
console.log("1. 自动修复功能会给包含特殊字符的文本添加引号");
console.log("2. 方向切换功能会在 TD/TB 和 LR/RL 之间切换");
console.log("3. 这些功能已集成到编辑器和主界面的按钮中");
